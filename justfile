# justfile documentation https://just.systems/man/en/chapter_1.html
# justfile cheatsheet https://cheatography.com/linux-china/cheat-sheets/justfile/

set dotenv-load

# Should match `foundry.toml` => `[rpc_endpoints]`
defaultForkedRpcUrl := "base"
defaultRpcUrl := 'base'

# Based on `ENVIROMENT` `.env` variable
# @see https://book.getfoundry.sh/reference/forge/forge-script#wallet-options---keystore
forgeScriptDeploymentParameters := if env_var('IS_DEVELOPMENT') == "true" { "--sender ****************************************** --unlocked --legacy" } else { "--account $KEYSTORE_PRODUCTION_DEPLOYER_ACCOUNT --slow --verify --sender $(cast wallet address --account $KEYSTORE_PRODUCTION_DEPLOYER_ACCOUNT) --etherscan-api-key $ETHERSCAN_API_KEY" }
forgeScriptManagementParameters := if env_var('IS_DEVELOPMENT') == "true" { "--sender ****************************************** --unlocked" } else { "--private-key $PRODUCTION_MANAGER_PRIVATE_KEY --slow" }

forgeScriptParameters := "--broadcast -vvvv"

default:
  @just --choose

install:
  forge install

lint:
  #!/usr/bin/env bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  npx solhint test/**/*.sol --config test/.solhint.json
  npx solhint src/**/*.sol --config src/.solhint.json

lint-fix:
  #!/usr/bin/env bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  npx solhint test/**/*.sol --config test/.solhint.json --fix --noPrompt
  npx solhint src/**/*.sol --config src/.solhint.json --fix --noPrompt
  forge fmt

docs:
  forge doc

docs-read:
  forge doc --serve

setup:
  #!/usr/bin/env bash
  bun install
  forge install
  forge compile
  cp .env.example .env

build:
  forge build --sizes

test:
  forge test --gas-report

static-analysis:
  slither . --config-file slither.config.json

coverage:
  forge clean
  forge coverage

gas:
  forge clean
  forge snapshot

anvil forkedRpcUrl=defaultForkedRpcUrl:
  anvil --rpc-url {{forkedRpcUrl}} --auto-impersonate --chain-id 31337 --port 8545 --block-time 10

outline:
  bun run outline-tests

tree:
  #!/usr/bin/env bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  bun run tree
  bulloak scaffold ./test/concrete/**/*.tree -s 0.8.23 -w

tree-check:
  #!/usr/bin/env bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  bulloak check ./test/concrete/**/*.tree

tree-fix:
  #!/usr/bin/env bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  bulloak check ./test/concrete/**/*.tree --fix
  forge fmt

mutation-test:
  vertigo run

formal-verification-presale:
  certoraRun test/formal-verification/conf/presale.conf

formal-verification-vest:
  certoraRun test/formal-verification/conf/vest.conf

formal-verification:
  certoraRun test/formal-verification/conf/vest.conf
  certoraRun test/formal-verification/conf/presale.conf

invariant-test:
  echidna echidna/TestPresale.sol --contract TestPresale --config echidna.config.yaml

assertion-test:
  echidna echidna/TestPresale.sol --contract TestPresale --config echidna.config.yaml --test-mode assertion

overflow-test:
  echidna echidna/TestPresale.sol --contract TestPresale --config echidna.config.yaml --test-mode overflow

deploy rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.deploy.s.sol:PresaleDeploy --rpc-url {{rpcUrl}} --sig 'run(string)' {{rpcUrl}} {{forgeScriptParameters}} {{forgeScriptDeploymentParameters}}
  bun run ./node-scripts/verify-vest-presale.ts --rpcUrl {{rpcUrl}}

add-round name startTimestamp endTimestamp whitelistRoot rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.addRound.s.sol:PresaleAddRound --rpc-url {{rpcUrl}} --sig 'run(string,string,uint256,uint256,bytes32)' {{rpcUrl}} {{name}} {{startTimestamp}} {{endTimestamp}} {{whitelistRoot}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}} 

update-round roundId name startTimestamp endTimestamp whitelistRoot rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.updateRound.s.sol:PresaleUpdateRound --rpc-url {{rpcUrl}} --sig 'run(string,uint256,string,uint256,uint256,bytes32)' {{rpcUrl}} {{roundId}} {{name}} {{startTimestamp}} {{endTimestamp}} {{whitelistRoot}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

update-round-whitelist roundId whitelistRoot rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.updateRoundWhitelist.s.sol:PresaleUpdateRoundWhitelist --rpc-url {{rpcUrl}} --sig 'run(string,uint256,bytes32)' {{rpcUrl}} {{roundId}} {{whitelistRoot}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

update-tge-timestamp timestamp rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.updateTgeTimestamp.s.sol:PresaleUpdateTgeTimestamp --rpc-url {{rpcUrl}} --sig 'run(string,uint256)' {{rpcUrl}} {{timestamp}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

update-listing-timestamp timestamp rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.updateListingTimestamp.s.sol:PresaleUpdateListingTimestamp --rpc-url {{rpcUrl}} --sig 'run(string,uint256)' {{rpcUrl}} {{timestamp}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

toggle-emergency-claimbacks rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.toggleEmergencyClaimbacks.s.sol:PresaleToggleEmergencyClaimbacks --rpc-url {{rpcUrl}} --sig 'run(string)' {{rpcUrl}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

deposit-token-a rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.depositTokenA.s.sol:PresaleDepositTokenA -vvvv --rpc-url {{rpcUrl}} --sig 'run(string)' {{rpcUrl}}{{forgeScriptParameters}} {{forgeScriptManagementParameters}}

withdraw-token-b rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.withdrawTokenB.s.sol:PresaleWithdrawTokenB --rpc-url {{rpcUrl}} --sig 'run(string)' {{rpcUrl}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

withdraw-token-a rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.withdrawTokenA.s.sol:PresaleWithdrawTokenA --rpc-url {{rpcUrl}} --sig 'run(string)' {{rpcUrl}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

transfer-liquidity-token-a-to-another-round rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/Presale.transferLiquidityTokenAToAnotherRound.s.sol:PresaleTransferLiquidityTokenAToAnotherRound --rpc-url {{rpcUrl}} --sig 'run(string)' {{rpcUrl}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}

# Transfer ERC20 tokens from current account to another wallet
# Configure TOKEN_ADDRESS, TO_ADDRESS, and TRANSFER_AMOUNT directly in the contract
# Example: just transfer-erc20 base
transfer-erc20 rpcUrl=defaultRpcUrl:
  forge clean
  forge script script/ERC20Transfer.s.sol:ERC20Transfer --rpc-url {{rpcUrl}} {{forgeScriptParameters}} {{forgeScriptManagementParameters}}
