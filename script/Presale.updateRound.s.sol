// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { PresaleScript } from "./PresaleScript.s.sol";

import { IVestMembership } from "src/IVestMembership.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Round } from "src/types/Round.sol";

contract PresaleUpdateRound is PresaleScript {
    function run(string memory rpcUrl, uint256 roundId) external {
        initializePresaleScriptConfig(rpcUrl);

        vm.startBroadcast();

        presale.updateRound(
            roundId,
            Round({
                name: "TGLP",
                startTimestamp: **********,
                endTimestamp: **********, // 48 hours after start (********** + 172800)
                whitelistRoot: 0x541ac561e2618e31601ba5e07a41bda0cf4823cddf9098c6fa5a4683084bda18,
                proofsUri: "",
                bonusNumerator: 0,
                bonusDenominator: 1,
                attributes: abi.encode(
                    IVestMembership.Attributes({
                        price: 350000, // 0.35 USDC (6 decimals)
                        allocation: 200000000000000000000000, // 200,000 tokens (18 decimals)
                        claimbackPeriod: 86400, // 24 hours
                        tgeNumerator: 25, // 25% TGE unlock
                        tgeDenominator: 100,
                        cliffDuration: 0, // No cliff
                        cliffNumerator: 0,
                        cliffDenominator: 1,
                        vestingPeriodCount: 240, // 240 periods
                        vestingPeriodDuration: 86400, // Daily vesting (86400 seconds)
                        tradeable: Boolean.FALSE
                    })
                )
            })
        );

        vm.stopBroadcast();
    }
}
