### Deploy & scripts related

# Will set `KEYSTORE_PRODUCTION_DEPLOYER_ACCOUNT` (false/production) or `******************************************` (true/development) as sender in scripts (see `justfile` & `script/`)
# @options true | false
# @see https://book.getfoundry.sh/cheatcodes/env-bool
IS_DEVELOPMENT=true

# Keystore of the deployer that will be used in scripts (see `justfile` & `script/`) in `production` environment
# @see https://book.getfoundry.sh/reference/forge/forge-script#wallet-options---keystore
KEYSTORE_PRODUCTION_DEPLOYER_ACCOUNT=tglp-deployer

# Keystore of the manager that will be used in scripts (see `justfile` & `script/`) in `production` environment
# @see https://book.getfoundry.sh/reference/forge/forge-script#wallet-options---keystore
KEYSTORE_PRODUCTION_MANAGER_ACCOUNT=tglp-deployer

# Private key of the deployer that will be used in management scripts (see `justfile` & `script/`) in `production` environment
# @see https://book.getfoundry.sh/reference/forge/forge-script#wallet-options---private-key
PRODUCTION_DEPLOYER_PRIVATE_KEY=

### Chain related

BSCSCAN_API_KEY=
ETHERSCAN_API_KEY=
BASESCAN_API_KEY=
POLYGONSCAN_API_KEY=
ARBISCAN_API_KEY=

### Variables used in test cases

TEST_TOKEN_A_ADDRESS=
TEST_TOKEN_A_DONOR_ADDRESS=

TEST_TOKEN_B_ADDRESS=
TEST_TOKEN_B_DONOR_ADDRESS=

TEST_TOKEN_A_DECIMALS=
TEST_TOKEN_B_DECIMALS=
